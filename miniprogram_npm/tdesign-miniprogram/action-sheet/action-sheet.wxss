.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-action-sheet__content {
  color: var(--td-action-sheet-color, var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9))));
  border-top-left-radius: var(--td-action-sheet-border-radius, var(--td-radius-extra-large, 24rpx));
  border-top-right-radius: var(--td-action-sheet-border-radius, var(--td-radius-extra-large, 24rpx));
  background-color: var(--td-bg-color-container, var(--td-font-white-1, #ffffff));
  overflow: hidden;
}
.t-action-sheet__content--grid {
  padding-top: 16rpx;
}
.t-action-sheet__content:focus {
  outline: 0;
}
.t-action-sheet__grid {
  padding-bottom: 16rpx;
}
.t-action-sheet__grid--swiper {
  padding-bottom: 48rpx;
}
.t-action-sheet__description {
  color: var(--td-action-sheet-description-color, var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4))));
  line-height: 44rpx;
  font-size: 28rpx;
  text-align: var(--td-action-sheet-text-align, center);
  padding: 24rpx 32rpx;
  position: relative;
}
.t-action-sheet__description:focus {
  outline: 0;
}
.t-action-sheet__description::after {
  content: '';
  display: block;
  position: absolute;
  top: unset;
  bottom: 0;
  left: unset;
  right: unset;
  background-color: var(--td-action-sheet-border-color, var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7)));
}
.t-action-sheet__description::after {
  height: 1px;
  left: 0;
  right: 0;
  transform: scaleY(0.5);
}
.t-action-sheet__description--left {
  text-align: left;
}
.t-action-sheet__description--left::after {
  left: 32rpx;
}
.t-action-sheet__list-item {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: var(--td-action-sheet-list-item-height, 112rpx);
  padding: 0 32rpx;
}
.t-action-sheet__list-item::after {
  content: '';
  display: block;
  position: absolute;
  top: unset;
  bottom: 0;
  left: unset;
  right: unset;
  background-color: var(--td-action-sheet-border-color, var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7)));
}
.t-action-sheet__list-item::after {
  height: 1px;
  left: 0;
  right: 0;
  transform: scaleY(0.5);
}
.t-action-sheet__list-item:focus {
  outline: 0;
}
.t-action-sheet__list-item--left {
  justify-content: start;
}
.t-action-sheet__list-item--left::after {
  left: 32rpx;
}
.t-action-sheet__list-item--disabled {
  color: var(--td-action-sheet-list-item-disabled-color, var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26))));
}
.t-action-sheet__list-item-text {
  font-size: var(--td-font-size-m, 32rpx);
  word-wrap: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.t-action-sheet__list-item-icon {
  margin-right: 16rpx;
}
.t-action-sheet__list-item-icon--suffix {
  margin-left: auto;
}
.t-action-sheet__swiper-wrap {
  margin-top: 8rpx;
  position: relative;
}
.t-action-sheet__footer {
  background-color: var(--td-bg-color-container, var(--td-font-white-1, #ffffff));
}
.t-action-sheet__gap-list {
  height: 16rpx;
  background-color: var(--td-action-sheet-gap-color, var(--td-bg-color-page, var(--td-gray-color-1, #f3f3f3)));
}
.t-action-sheet__gap-grid {
  height: 1rpx;
  background-color: var(--td-action-sheet-border-color, var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7)));
}
.t-action-sheet__cancel {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--td-action-sheet-cancel-color, var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9))));
  height: var(--td-action-sheet-cancel-height, 96rpx);
}
.t-action-sheet__dots {
  position: absolute;
  left: 50%;
  bottom: 32rpx;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
}
.t-action-sheet__dots-item {
  width: 16rpx;
  height: 16rpx;
  background-color: #dcdcdc;
  border-radius: 50%;
  margin: 0 16rpx;
  transition: all 0.4s ease-in;
}
.t-action-sheet__dots-item.t-is-active {
  background-color: #0052d9;
}
