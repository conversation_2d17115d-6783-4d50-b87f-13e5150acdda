.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-badge {
  position: relative;
  display: inline-flex;
  align-items: start;
}
.t-badge--basic {
  z-index: 100;
  padding: 0 var(--td-badge-basic-padding, 8rpx);
  font-size: var(--td-badge-font-size, var(--td-font-size-xs, var(--td-font-size, 20rpx)));
  color: var(--td-badge-text-color, var(--td-text-color-anti, var(--td-font-white-1, #ffffff)));
  background-color: var(--td-badge-bg-color, var(--td-error-color, var(--td-error-color-6, #d54941)));
  text-align: center;
  height: var(--td-badge-basic-height, 32rpx);
  line-height: var(--td-badge-basic-height, 32rpx);
  font-weight: var(--td-badge-font-weight, 600);
  border-radius: var(--td-badge-border-radius, 4rpx);
}
.t-badge--dot {
  height: var(--td-badge-dot-size, 16rpx);
  border-radius: 50%;
  min-width: var(--td-badge-dot-size, 16rpx);
  padding: 0;
}
.t-badge--count {
  min-width: var(--td-badge-basic-width, 32rpx);
  white-space: nowrap;
  box-sizing: border-box;
}
.t-badge--circle {
  border-radius: calc(var(--td-badge-basic-height, 32rpx) / 2);
}
.t-badge__ribbon-outer {
  position: absolute;
  top: 0;
  right: 0;
}
.t-badge--ribbon {
  position: relative;
  display: inline-block;
  transform-origin: center center;
  transform: translate(calc(50% - var(--td-badge-basic-height, 32rpx) + 1rpx), calc(-50% + var(--td-badge-basic-height, 32rpx) - 1rpx)) rotate(45deg);
  border-radius: 0;
}
.t-badge--ribbon::before,
.t-badge--ribbon::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  bottom: 0;
  border-bottom: var(--td-badge-basic-height, 32rpx) solid var(--td-badge-bg-color, var(--td-error-color, var(--td-error-color-6, #d54941)));
  font-size: 0;
}
.t-badge--ribbon::before {
  left: calc(-1 * var(--td-badge-basic-height, 32rpx) + 1rpx);
  border-left: var(--td-badge-basic-height, 32rpx) solid transparent;
}
.t-badge--ribbon::after {
  right: calc(-1 * var(--td-badge-basic-height, 32rpx) + 1rpx);
  border-right: var(--td-badge-basic-height, 32rpx) solid transparent;
}
.t-badge--bubble {
  border-radius: var(--td-badge-bubble-border-radius, 20rpx 20rpx 20rpx 1px);
}
.t-badge--large {
  font-size: var(--td-badge-large-font-size, var(--td-font-size-s, 24rpx));
  height: var(--td-badge-large-height, 40rpx);
  min-width: var(--td-badge-large-height, 40rpx);
  line-height: var(--td-badge-large-height, 40rpx);
  padding: 0 var(--td-badge-large-padding, 10rpx);
}
.t-badge--large.t-badge--circle {
  border-radius: calc(var(--td-badge-large-height, 40rpx) / 2);
}
.t-badge__content:not(:empty) + .t-has-count {
  transform-origin: center center;
  transform: translate(-50%, -50%);
  position: absolute;
  left: 100%;
  top: 0;
}
.t-badge__content-text {
  display: block;
  line-height: 48rpx;
  color: var(--td-badge-content-text-color, var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9))));
}
