<wxs src="../common/utils.wxs" module="_" />

<view style="{{_._style([style, customStyle])}}" class="{{className}} class">
  <slot />
  <!-- 自定义折叠元素 -->
  <view class="{{classPrefix}}__collapse--slot">
    <slot name="collapse-avatar" />
  </view>
  <!-- 默认折叠元素 -->
  <view class="{{classPrefix}}__collapse--default" wx:if="{{max && (max < length)}}" bindtap="onCollapsedItemClick">
    <t-avatar
      t-class-image="{{prefix}}-avatar--border {{prefix}}-avatar--border-{{size}} {{prefix}}-class-image"
      t-class-content="{{prefix}}-class-content"
      size="{{size}}"
      shape="{{shape}}"
      icon="{{ collapseAvatar ? '' : 'user-add'}}"
      aria-role="none"
      >{{collapseAvatar}}</t-avatar
    >
  </view>
</view>
